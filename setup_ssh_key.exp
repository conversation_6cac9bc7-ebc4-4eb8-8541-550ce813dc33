#!/usr/bin/expect -f

set timeout 30
set password "design@123"
set user "ubuntu"
set host "**************"

# 读取公钥内容
set fp [open "$env(HOME)/.ssh/id_ed25519.pub" r]
set public_key [read $fp]
close $fp
set public_key [string trim $public_key]

spawn ssh $user@$host "mkdir -p ~/.ssh && echo '$public_key' >> ~/.ssh/authorized_keys && chmod 700 ~/.ssh && chmod 600 ~/.ssh/authorized_keys && echo 'SSH key added successfully'"

expect {
    "Are you sure you want to continue connecting" {
        send "yes\r"
        exp_continue
    }
    "password:" {
        send "$password\r"
        exp_continue
    }
    "SSH key added successfully" {
        puts "SSH key setup completed successfully!"
        exit 0
    }
    timeout {
        puts "Connection timed out"
        exit 1
    }
    eof {
        puts "Connection ended"
        exit 0
    }
}

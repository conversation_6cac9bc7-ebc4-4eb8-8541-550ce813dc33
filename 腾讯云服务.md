
# JeecgBoot 腾讯云服务器部署文档

## 服务器信息
- **操作系统**: Ubuntu 24.04 LTS
- **CPU**: 4核 (Intel Xeon Platinum 8255C @ 2.50GHz)
- **内存**: 8GB (7.5GB 可用)
- **硬盘**: 180GB
- **Swap**: 2GB

## 用户信息
- **IP**: **************
- **账号**: ubuntu
- **密码**: design@123

## 项目路径
- **项目根目录**: `/home/<USER>/project/JeecgBoot`
- **数据持久化目录**: `/home/<USER>/project/data`

## SSH 连接方式

### 方式一：密码连接
```bash
ssh ubuntu@**************
# 输入密码：design@123
```

### 方式二：密钥连接（推荐）
```bash
# 使用项目中的 setup_ssh_key.exp 脚本自动配置SSH密钥
./setup_ssh_key.exp
```

## 项目架构

JeecgBoot 是一个基于 Spring Boot + Vue3 的前后端分离项目：

- **后端**: Spring Boot 3.8.1 + Java 17
- **前端**: Vue3 + Ant Design Vue
- **数据库**: MySQL 8.0.19
- **缓存**: Redis 5.0
- **容器化**: Docker + Docker Compose

## 服务端口说明

| 服务 | 端口 | 说明 |
|------|------|------|
| 前端 (Nginx) | 80 | Web访问入口 |
| 后端 (Spring Boot) | 8080 | API服务 |
| MySQL | 3306 | 数据库服务 |
| Redis | 6379 | 缓存服务 |

## 部署方式

### 1. 快速部署（推荐）

使用改进的 docker-compose 配置，支持数据持久化：

```bash
# 进入项目目录
cd /home/<USER>/project/JeecgBoot

# 停止现有服务（如果有）
docker-compose down

# 创建数据持久化目录
mkdir -p /home/<USER>/project/data/mysql
mkdir -p /home/<USER>/project/data/redis
mkdir -p /home/<USER>/project/data/upload

# 使用改进的配置启动服务
docker-compose -f docker-compose-production.yml up -d --build
```

### 2. 改进的 Docker Compose 配置

#### 方式一：使用现有的 docker-compose.yml（简单部署）

```bash
# 进入项目目录
cd /home/<USER>/project/JeecgBoot

# 直接启动（不支持数据持久化）
docker-compose up -d --build
```

#### 方式二：使用生产环境配置（推荐）

创建 `docker-compose-production.yml` 文件，支持数据持久化：

```yaml
version: '3.8'
services:
  jeecg-boot-mysql:
    build:
      context: ./jeecg-boot/db
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_ROOT_HOST: '%'
      TZ: Asia/Shanghai
    restart: always
    container_name: jeecg-boot-mysql
    image: jeecg-boot-mysql
    command:
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --max_allowed_packet=128M
      --default-authentication-plugin=caching_sha2_password
    ports:
      - "3306:3306"
    volumes:
      # 数据持久化 - 避免容器重建后数据丢失
      - /home/<USER>/project/data/mysql:/var/lib/mysql
      # 配置文件持久化
      - /home/<USER>/project/data/mysql/conf:/etc/mysql/conf.d
    networks:
      - jeecg-boot

  jeecg-boot-redis:
    image: registry.cn-hangzhou.aliyuncs.com/jeecgdocker/redis:5.0
    ports:
      - "6379:6379"
    restart: always
    hostname: jeecg-boot-redis
    container_name: jeecg-boot-redis
    volumes:
      # Redis数据持久化
      - /home/<USER>/project/data/redis:/data
    command: redis-server --appendonly yes
    networks:
      - jeecg-boot

  jeecg-boot-system:
    build:
      context: ./jeecg-boot/jeecg-module-system/jeecg-system-start
    restart: on-failure
    depends_on:
      - jeecg-boot-mysql
      - jeecg-boot-redis
    container_name: jeecg-boot-system
    image: jeecg-boot-system
    hostname: jeecg-boot-system
    ports:
      - "8080:8080"
    volumes:
      # 文件上传目录持久化
      - /home/<USER>/project/data/upload:/jeecg-boot/upload
      # 日志目录持久化
      - /home/<USER>/project/data/logs:/jeecg-boot/logs
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      # 针对4核8G服务器的JVM优化
      - JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
    networks:
      - jeecg-boot

  jeecg-vue:
    build:
      context: ./jeecgboot-vue3
    container_name: jeecgboot-vue3-nginx
    image: jeecgboot-vue3
    depends_on:
      - jeecg-boot-system
    networks:
      - jeecg-boot
    ports:
      - "80:80"
    restart: always

networks:
  jeecg-boot:
    name: jeecg_boot
    driver: bridge

volumes:
  mysql-data:
    driver: local
  redis-data:
    driver: local
  upload-data:
    driver: local
```

#### 方式三：一键部署脚本（最推荐）

创建 `deploy.sh` 自动化部署脚本：

```bash
#!/bin/bash

# JeecgBoot 快速部署脚本
# 作者: 系统管理员
# 日期: 2025-07-22

set -e

echo "=========================================="
echo "JeecgBoot 快速部署脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 项目目录
PROJECT_DIR="/home/<USER>/project/JeecgBoot"
DATA_DIR="/home/<USER>/project/data"

# 检查是否在项目目录中
if [ ! -f "docker-compose-production.yml" ]; then
    echo -e "${RED}错误: 请在项目根目录中运行此脚本${NC}"
    exit 1
fi

# 函数：打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 函数：检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_error "$1 命令未找到，请先安装 $1"
        exit 1
    fi
}

# 检查必要的命令
print_info "检查必要的命令..."
check_command "docker"
check_command "docker-compose"
check_command "git"

# 创建数据目录
print_info "创建数据持久化目录..."
mkdir -p ${DATA_DIR}/{mysql,redis,upload,logs,backup}

# 备份当前配置
print_info "备份本地配置..."
git stash

# 拉取最新代码
print_info "拉取最新代码..."
git pull origin master

# 恢复本地配置
print_info "恢复本地配置..."
git stash pop || print_warning "没有需要恢复的配置"

# 停止现有服务
print_info "停止现有服务..."
docker-compose down || print_warning "没有运行中的服务"

# 清理悬空镜像
print_info "清理悬空镜像..."
docker image prune -f

# 使用生产环境配置启动服务
print_info "启动服务（使用数据持久化配置）..."
docker-compose -f docker-compose-production.yml up -d --build

# 等待服务启动
print_info "等待服务启动..."
sleep 30

# 检查服务状态
print_info "检查服务状态..."
docker-compose -f docker-compose-production.yml ps

# 检查后端服务日志
print_info "检查后端服务启动日志..."
docker logs jeecg-boot-system --tail 10

echo "=========================================="
print_info "部署完成！"
echo "前端访问地址: http://$(curl -s ifconfig.me || echo '**************')"
echo "后端API地址: http://$(curl -s ifconfig.me || echo '**************'):8080"
echo "默认账号: admin / 123456"
echo "=========================================="

# 显示有用的命令
echo ""
echo "常用命令:"
echo "查看服务状态: docker-compose -f docker-compose-production.yml ps"
echo "查看日志: docker-compose -f docker-compose-production.yml logs -f [服务名]"
echo "重启服务: docker-compose -f docker-compose-production.yml restart [服务名]"
echo "停止服务: docker-compose -f docker-compose-production.yml down"
echo ""
```

使用方法：
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

## 常用运维命令

### 服务管理

#### 方式一：使用默认配置
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart jeecg-boot-system

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f jeecg-boot-system
```

#### 方式二：使用生产环境配置
```bash
# 启动所有服务
docker-compose -f docker-compose-production.yml up -d

# 停止所有服务
docker-compose -f docker-compose-production.yml down

# 重启特定服务
docker-compose -f docker-compose-production.yml restart jeecg-boot-system

# 查看服务状态
docker-compose -f docker-compose-production.yml ps

# 查看服务日志
docker-compose -f docker-compose-production.yml logs -f jeecg-boot-system
```

#### 方式三：使用一键部署脚本
```bash
# 一键部署（推荐）
./deploy.sh

# 手动停止服务
docker-compose -f docker-compose-production.yml down
```

### 代码更新部署
```bash
# 1. 进入项目目录
cd /home/<USER>/project/JeecgBoot

# 2. 备份本地修改
git stash

# 3. 拉取最新代码
git pull origin master

# 4. 恢复本地配置
git stash pop

# 5. 重新构建并启动服务
docker-compose -f docker-compose-production.yml up -d --build

# 6. 查看启动状态
docker-compose -f docker-compose-production.yml ps
```

### 数据备份
```bash
# MySQL数据备份
docker exec jeecg-boot-mysql mysqldump -uroot -proot --all-databases > /home/<USER>/project/backup/mysql_backup_$(date +%Y%m%d_%H%M%S).sql

# Redis数据备份
docker exec jeecg-boot-redis redis-cli BGSAVE
cp /home/<USER>/project/data/redis/dump.rdb /home/<USER>/project/backup/redis_backup_$(date +%Y%m%d_%H%M%S).rdb
```

## 监控和日志

### 查看容器状态
```bash
# 查看所有容器状态
docker ps

# 查看容器资源使用情况
docker stats

# 查看特定容器详细信息
docker inspect jeecg-boot-system
```

### 日志查看
```bash
# 查看后端服务日志
docker logs -f jeecg-boot-system

# 查看前端服务日志
docker logs -f jeecgboot-vue3-nginx

# 查看数据库日志
docker logs -f jeecg-boot-mysql

# 查看Redis日志
docker logs -f jeecg-boot-redis
```

## 故障排查

### 常见问题

1. **服务启动失败**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep :8080

   # 检查容器日志
   docker logs jeecg-boot-system
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL容器状态
   docker exec -it jeecg-boot-mysql mysql -uroot -proot

   # 检查网络连接
   docker network ls
   docker network inspect jeecg_boot
   ```

3. **前端访问异常**
   ```bash
   # 检查Nginx配置
   docker exec -it jeecgboot-vue3-nginx nginx -t

   # 重启前端服务
   docker-compose restart jeecg-vue
   ```

## 安全配置

### 防火墙设置
```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw allow 8080  # API (可选，建议通过Nginx代理)

# 启用防火墙
sudo ufw enable
```

### 定期维护
```bash
# 清理无用的Docker镜像和容器
docker system prune -a

# 更新系统包
sudo apt update && sudo apt upgrade -y

# 定期备份数据
# 建议设置cron定时任务进行自动备份
```

## 性能优化建议

### 针对4核8G配置的优化

1. **JVM 参数优化**
   ```bash
   # 在 docker-compose-production.yml 中添加 JVM 参数
   environment:
     - SPRING_PROFILES_ACTIVE=prod
     - JAVA_OPTS=-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
   ```

2. **数据库优化**
   ```bash
   # MySQL 配置优化 (针对8G内存)
   innodb_buffer_pool_size = 3G
   innodb_log_file_size = 256M
   max_connections = 200
   query_cache_size = 128M
   ```

3. **Redis 优化**
   ```bash
   # Redis 内存限制 (预留1G给Redis)
   maxmemory 1gb
   maxmemory-policy allkeys-lru
   ```

4. **系统级优化**
   ```bash
   # 文件描述符限制
   echo "* soft nofile 65536" >> /etc/security/limits.conf
   echo "* hard nofile 65536" >> /etc/security/limits.conf

   # 网络优化
   echo "net.core.somaxconn = 1024" >> /etc/sysctl.conf
   echo "net.ipv4.tcp_max_syn_backlog = 1024" >> /etc/sysctl.conf
   ```

5. **监控建议**
   - CPU使用率保持在70%以下
   - 内存使用率保持在80%以下
   - 磁盘I/O监控
   - 定期清理Docker镜像和容器

## 部署方式对比

| 特性 | 方式一：默认配置 | 方式二：生产配置 | 方式三：一键脚本 |
|------|------------------|------------------|------------------|
| **数据持久化** | ❌ 不支持 | ✅ 完全支持 | ✅ 完全支持 |
| **JVM优化** | ❌ 默认配置 | ✅ 4核8G优化 | ✅ 4核8G优化 |
| **自动化程度** | ⚠️ 手动操作 | ⚠️ 手动操作 | ✅ 全自动 |
| **代码更新** | ⚠️ 手动拉取 | ⚠️ 手动拉取 | ✅ 自动拉取 |
| **配置保护** | ❌ 可能丢失 | ❌ 可能丢失 | ✅ 自动备份 |
| **适用场景** | 开发测试 | 生产环境 | 生产环境 |
| **推荐指数** | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

### 推荐使用方式

1. **开发测试环境**：使用方式一（默认配置）
2. **生产环境首次部署**：使用方式三（一键脚本）
3. **生产环境日常维护**：使用方式二（生产配置）

## 访问地址

- **前端访问**: http://**************
- **后端API**: http://**************:8080
- **默认账号**: admin / 123456

## 快速开始

### 新用户推荐流程
```bash
# 1. SSH连接服务器
ssh ubuntu@**************

# 2. 进入项目目录
cd /home/<USER>/project/JeecgBoot

# 3. 一键部署（推荐）
./deploy.sh
```

### 日常维护流程
```bash
# 查看服务状态
docker-compose -f docker-compose-production.yml ps

# 查看日志
docker-compose -f docker-compose-production.yml logs -f jeecg-boot-system

# 重启服务
docker-compose -f docker-compose-production.yml restart jeecg-boot-system
```

---

**最后更新时间**: 2025-07-22
**维护人员**: 系统管理员
**服务器配置**: 4核8G Ubuntu 24.04 LTS